# 激光绘图系统使用说明

## 📋 系统概述

激光绘图系统是一个基于函数化设计的激光绘图解决方案，支持通过数学函数绘制任意图形。系统建立了纸张坐标系到电机脉冲坐标系的精确映射，以纸张中心为原点，右边为X轴正半轴，上边为Y轴正半轴。

## 🎯 核心特性

- **函数化绘图**：支持直角坐标函数、参数方程、极坐标函数
- **精确坐标映射**：纸张四角到脉冲值的精确对应
- **循环绘制控制**：通过宏定义控制单次或循环绘制
- **预定义函数库**：内置常用数学函数
- **扩展性强**：易于添加自定义函数

## ⚙️ 配置参数

### 关键宏定义 (app_laser_draw.h)

```c
#define LASER_DRAW_LOOP_MODE 0        // 0:单次绘制 1:循环绘制
#define LASER_DRAW_STEP_DELAY 5       // 绘制步长延时(ms)
#define LASER_COORDINATE_SCALE 100    // 坐标缩放比例(脉冲/单位)
```

### 纸张坐标映射

```c
// 纸张四角脉冲坐标(左上角开始顺时针)
#define PAPER_TOP_LEFT_X     -5000    // 左上角X脉冲
#define PAPER_TOP_LEFT_Y     5000     // 左上角Y脉冲
#define PAPER_TOP_RIGHT_X    5000     // 右上角X脉冲
#define PAPER_TOP_RIGHT_Y    5000     // 右上角Y脉冲
#define PAPER_BOTTOM_RIGHT_X 5000     // 右下角X脉冲
#define PAPER_BOTTOM_RIGHT_Y -5000    // 右下角Y脉冲
#define PAPER_BOTTOM_LEFT_X  -5000    // 左下角X脉冲
#define PAPER_BOTTOM_LEFT_Y  -5000    // 左下角Y脉冲
```

### 激光控制IO

```c
#define LASER_GPIO_PORT GPIOB         // 激光控制GPIO端口
#define LASER_GPIO_PIN  GPIO_PIN_0    // 激光控制GPIO引脚
```

## 🚀 快速开始

### 1. 系统初始化

```c
#include "app_laser_draw.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    // ... 其他初始化代码
    
    // 初始化激光绘图系统
    app_laser_draw_init();
    
    // 开始绘图
    laser_draw_examples();
}
```

### 2. 基本图形绘制

```c
// 绘制直线
laser_point_t start = {-2.0f, -1.0f};
laser_point_t end = {2.0f, 1.0f};
draw_line(start, end);

// 绘制矩形
laser_point_t corner = {-1.5f, -1.0f};
draw_rectangle(corner, 3.0f, 2.0f);

// 绘制圆形
laser_point_t center = {0.0f, 0.0f};
draw_circle(center, 1.5f);
```

### 3. 函数图形绘制

#### 直角坐标函数 y=f(x)

```c
// 绘制正弦波
draw_function_t sine_func = {
    .func_xy = sine_function,
    .param_x = NULL,
    .param_y = NULL,
    .polar_r = NULL
};

draw_params_t params = {
    .x_min = -3.14f,
    .x_max = 3.14f,
    .step = 0.1f,
    .scale = 1.0f
};

start_drawing(&sine_func, params);
```

#### 参数方程 x=f(t), y=g(t)

```c
// 绘制圆形(参数方程)
draw_function_t circle_func = {
    .func_xy = NULL,
    .param_x = circle_param_x,
    .param_y = circle_param_y,
    .polar_r = NULL
};

draw_params_t params = {
    .t_min = 0.0f,
    .t_max = 6.28f, // 2π
    .step = 0.1f,
    .scale = 1.5f
};

start_drawing(&circle_func, params);
```

#### 极坐标函数 r=f(θ)

```c
// 绘制心形线
draw_function_t heart_func = {
    .func_xy = NULL,
    .param_x = NULL,
    .param_y = NULL,
    .polar_r = heart_polar_r
};

draw_params_t params = {
    .theta_min = 0.0f,
    .theta_max = 6.28f, // 2π
    .step = 0.05f,
    .scale = 1.0f
};

start_drawing(&heart_func, params);
```

## 📚 预定义函数库

### 直角坐标函数
- `line_function(x)` - 直线函数
- `sine_function(x)` - 正弦函数
- `cosine_function(x)` - 余弦函数
- `parabola_function(x)` - 抛物线函数

### 参数方程函数
- `circle_param_x(t)`, `circle_param_y(t)` - 圆的参数方程

### 极坐标函数
- `heart_polar_r(theta)` - 心形线
- `rose_polar_r(theta)` - 玫瑰花
- `spiral_polar_r(theta)` - 阿基米德螺旋

## 🔧 自定义函数

### 创建自定义函数

```c
// 自定义波浪函数
float custom_wave_function(float x)
{
    return sin(x) + 0.5f * sin(3 * x);
}

// 使用自定义函数
draw_function_t wave_func = {
    .func_xy = custom_wave_function,
    .param_x = NULL,
    .param_y = NULL,
    .polar_r = NULL
};

draw_params_t params = {
    .x_min = -6.28f,
    .x_max = 6.28f,
    .step = 0.1f,
    .scale = 1.0f
};

start_drawing(&wave_func, params);
```

## 🔄 循环绘制控制

### 启用循环模式

1. 修改 `app_laser_draw.h` 中的宏定义：
```c
#define LASER_DRAW_LOOP_MODE 1  // 启用循环绘制
```

2. 使用循环绘制：
```c
// 开始循环绘制
start_drawing(&func, params);

// 需要停止时调用
stop_drawing();
```

## 📐 坐标系统说明

### 坐标系定义
- **原点**：纸张中心
- **X轴正方向**：向右
- **Y轴正方向**：向上
- **单位**：可通过 `LASER_COORDINATE_SCALE` 调整

### 坐标转换
- 逻辑坐标 → 脉冲坐标：`convert_to_pulse(x, y)`
- 脉冲坐标 → 逻辑坐标：`convert_to_coordinate(pulse_x, pulse_y)`

## ⚠️ 注意事项

1. **激光安全**：确保激光功率适当，避免长时间照射同一点
2. **坐标范围**：绘制前确认坐标在纸张范围内
3. **步长设置**：步长过小会导致绘制缓慢，过大会影响精度
4. **延时控制**：通过 `LASER_DRAW_STEP_DELAY` 控制绘制速度
5. **内存使用**：复杂函数可能消耗较多计算资源

## 🎨 应用示例

详细的使用示例请参考 `app_laser_draw_example.c` 文件，包含：
- 基本图形绘制示例
- 数学函数绘制示例
- 自定义函数示例
- 循环绘制示例

## 🔧 故障排除

1. **激光不亮**：检查GPIO配置和硬件连接
2. **绘制位置偏移**：检查纸张四角坐标配置
3. **绘制不平滑**：调整步长和延时参数
4. **编译错误**：确保包含必要的头文件和数学库
