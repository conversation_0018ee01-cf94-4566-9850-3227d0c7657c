// Copyright (c) 2024 白蛋电子工作室

#include "app_laser_draw.h"

// 数学常数定义
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 激光绘图状态变量
static laser_draw_state_t draw_state = LASER_DRAW_IDLE;
static bool laser_state = false;

// 当前绘图参数
static draw_function_t current_function;
static draw_params_t current_params;
static float current_progress = 0.0f;
static bool first_point = true;

// 绘图循环控制
static bool draw_loop_enabled = LASER_DRAW_LOOP_MODE;
static uint32_t draw_count = 0;

// 绘图定时器
static MultiTimer mt_laser_draw;

/**
 * @brief 激光绘图系统初始化
 */
void app_laser_draw_init(void)
{
    // TODO: 初始化激光控制GPIO
    // GPIO_InitTypeDef GPIO_InitStruct = {0};
    // __HAL_RCC_GPIOB_CLK_ENABLE();
    // GPIO_InitStruct.Pin = LASER_GPIO_PIN;
    // GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    // GPIO_InitStruct.Pull = GPIO_NOPULL;
    // GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    // HAL_GPIO_Init(LASER_GPIO_PORT, &GPIO_InitStruct);

    // 初始状态关闭激光
    laser_off();

    // 初始化绘图状态
    draw_state = LASER_DRAW_IDLE;
}

/**
 * @brief 激光开启
 */
void laser_on(void)
{
    // TODO: 实际GPIO控制
    // HAL_GPIO_WritePin(LASER_GPIO_PORT, LASER_GPIO_PIN, GPIO_PIN_SET);
    laser_state = true;
}

/**
 * @brief 激光关闭
 */
void laser_off(void)
{
    // TODO: 实际GPIO控制
    // HAL_GPIO_WritePin(LASER_GPIO_PORT, LASER_GPIO_PIN, GPIO_PIN_RESET);
    laser_state = false;
}

/**
 * @brief 坐标转换为脉冲值
 * @param x X坐标(以纸张中心为原点)
 * @param y Y坐标(以纸张中心为原点)
 * @return 脉冲坐标点
 */
pulse_point_t convert_to_pulse(float x, float y)
{
    pulse_point_t pulse;

    // 将坐标转换为脉冲值(以纸张中心为原点)
    pulse.x = PAPER_CENTER_X + (int32_t)(x * LASER_COORDINATE_SCALE);
    pulse.y = PAPER_CENTER_Y + (int32_t)(y * LASER_COORDINATE_SCALE);

    return pulse;
}

/**
 * @brief 脉冲值转换为坐标
 * @param pulse_x X轴脉冲值
 * @param pulse_y Y轴脉冲值
 * @return 坐标点
 */
laser_point_t convert_to_coordinate(int32_t pulse_x, int32_t pulse_y)
{
    laser_point_t point;

    // 将脉冲值转换为坐标(以纸张中心为原点)
    point.x = (float)(pulse_x - PAPER_CENTER_X) / LASER_COORDINATE_SCALE;
    point.y = (float)(pulse_y - PAPER_CENTER_Y) / LASER_COORDINATE_SCALE;

    return point;
}

/**
 * @brief 移动到指定点(激光关闭)
 * @param x X坐标
 * @param y Y坐标
 */
void move_to_point(float x, float y)
{
    pulse_point_t pulse = convert_to_pulse(x, y);

    // 关闭激光
    laser_off();

    // 移动到目标位置
    Motor_Set_Position(pulse.x, pulse.y);
}

/**
 * @brief 绘制到指定点(激光开启)
 * @param x X坐标
 * @param y Y坐标
 */
void draw_to_point(float x, float y)
{
    pulse_point_t pulse = convert_to_pulse(x, y);

    // 开启激光
    laser_on();

    // 移动到目标位置
    Motor_Set_Position(pulse.x, pulse.y);
}

/**
 * @brief 绘制直线
 * @param start 起点
 * @param end 终点
 */
void draw_line(laser_point_t start, laser_point_t end)
{
    // 移动到起点
    move_to_point(start.x, start.y);

    // 绘制到终点
    draw_to_point(end.x, end.y);

    // 关闭激光
    laser_off();
}

/**
 * @brief 绘制矩形
 * @param corner 左下角坐标
 * @param width 宽度
 * @param height 高度
 */
void draw_rectangle(laser_point_t corner, float width, float height)
{
    laser_point_t points[4];

    // 计算四个角点
    points[0] = (laser_point_t){corner.x, corner.y};                  // 左下角
    points[1] = (laser_point_t){corner.x + width, corner.y};          // 右下角
    points[2] = (laser_point_t){corner.x + width, corner.y + height}; // 右上角
    points[3] = (laser_point_t){corner.x, corner.y + height};         // 左上角

    // 移动到起点
    move_to_point(points[0].x, points[0].y);

    // 开启激光绘制矩形
    laser_on();
    for (int i = 1; i < 4; i++)
    {
        draw_to_point(points[i].x, points[i].y);
    }
    // 回到起点闭合
    draw_to_point(points[0].x, points[0].y);

    // 关闭激光
    laser_off();
}

/**
 * @brief 绘制圆形
 * @param center 圆心
 * @param radius 半径
 */
void draw_circle(laser_point_t center, float radius)
{
    float step = 0.1f; // 角度步长
    bool first_point = true;

    for (float theta = 0; theta <= 2 * M_PI + step; theta += step)
    {
        float x = center.x + radius * cos(theta);
        float y = center.y + radius * sin(theta);

        if (first_point)
        {
            move_to_point(x, y);
            laser_on();
            first_point = false;
        }
        else
        {
            draw_to_point(x, y);
        }
    }

    // 关闭激光
    laser_off();
}

/**
 * @brief 激光绘图任务函数
 * 由定时器回调函数调用，逐步执行绘图
 */
void app_laser_draw_task(MultiTimer *timer, void *userData)
{
    if (draw_state != LASER_DRAW_DRAWING)
    {
        return;
    }

    float x, y;
    bool draw_complete = false;

    // 根据函数类型计算当前点坐标
    if (current_function.func_xy != NULL)
    {
        // 直角坐标函数 y=f(x)
        if (current_progress <= current_params.x_max)
        {
            x = current_progress * current_params.scale;
            y = current_function.func_xy(current_progress) * current_params.scale;
        }
        else
        {
            draw_complete = true;
        }
    }
    else if (current_function.param_x != NULL && current_function.param_y != NULL)
    {
        // 参数方程 x=f(t), y=g(t)
        if (current_progress <= current_params.t_max)
        {
            x = current_function.param_x(current_progress) * current_params.scale;
            y = current_function.param_y(current_progress) * current_params.scale;
        }
        else
        {
            draw_complete = true;
        }
    }
    else if (current_function.polar_r != NULL)
    {
        // 极坐标函数 r=f(θ)
        if (current_progress <= current_params.theta_max)
        {
            float r = current_function.polar_r(current_progress) * current_params.scale;
            x = r * cos(current_progress);
            y = r * sin(current_progress);
        }
        else
        {
            draw_complete = true;
        }
    }

    // 执行绘图动作
    if (!draw_complete)
    {
        if (first_point)
        {
            move_to_point(x, y);
            first_point = false;
        }
        else
        {
            draw_to_point(x, y);
        }

        // 递增进度到下一个点
        current_progress += current_params.step;

        // 继续下一步绘制
        multiTimerStart(&mt_laser_draw, LASER_DRAW_STEP_DELAY, app_laser_draw_task, NULL);
    }
    else
    {
        // 绘制完成
        laser_off();
        draw_count++;

        // 检查是否需要循环绘制
        if (draw_loop_enabled)
        {
            // 重新开始绘制
            current_progress = (current_function.func_xy != NULL) ? current_params.x_min : (current_function.param_x != NULL) ? current_params.t_min
                                                                                                                              : current_params.theta_min;
            first_point = true;
            multiTimerStart(&mt_laser_draw, LASER_DRAW_STEP_DELAY, app_laser_draw_task, NULL);
        }
        else
        {
            draw_state = LASER_DRAW_IDLE; // 绘制完成后重置为空闲状态
        }
    }
}

/**
 * @brief 开始绘制函数图形
 * @param func 绘图函数结构体指针
 * @param params 绘图参数
 */
void start_drawing(draw_function_t *func, draw_params_t params)
{
    if (draw_state == LASER_DRAW_DRAWING)
    {
        return; // 如果正在绘制，直接返回
    }

    // 保存绘图参数
    current_function = *func;
    current_params = params;
    draw_count = 0;
    first_point = true;

    // 设置初始进度
    if (current_function.func_xy != NULL)
    {
        current_progress = current_params.x_min;
    }
    else if (current_function.param_x != NULL && current_function.param_y != NULL)
    {
        current_progress = current_params.t_min;
    }
    else if (current_function.polar_r != NULL)
    {
        current_progress = current_params.theta_min;
    }

    // 设置绘制状态
    draw_state = LASER_DRAW_DRAWING;

    // 启动绘图定时器
    multiTimerStart(&mt_laser_draw, LASER_DRAW_STEP_DELAY, app_laser_draw_task, NULL);
}

/**
 * @brief 停止绘制
 */
void stop_drawing(void)
{
    draw_state = LASER_DRAW_IDLE;
    laser_off(); // 关闭激光
}

/**
 * @brief 获取绘制状态
 * @return 当前绘制状态
 */
laser_draw_state_t get_draw_state(void)
{
    return draw_state;
}

/* 预定义常用数学函数 */

/**
 * @brief 直线函数 y = kx + b
 */
float line_function(float x)
{
    return 0.5f * x + 1.0f; // 示例：斜率0.5，截距1
}

/**
 * @brief 正弦函数 y = sin(x)
 */
float sine_function(float x)
{
    return sin(x);
}

/**
 * @brief 余弦函数 y = cos(x)
 */
float cosine_function(float x)
{
    return cos(x);
}

/**
 * @brief 抛物线函数 y = x²
 */
float parabola_function(float x)
{
    return x * x;
}

/**
 * @brief 圆的参数方程 - X分量
 */
float circle_param_x(float t)
{
    return cos(t);
}

/**
 * @brief 圆的参数方程 - Y分量
 */
float circle_param_y(float t)
{
    return sin(t);
}

/**
 * @brief 心形线极坐标函数 r = a(1-cos(θ))
 */
float heart_polar_r(float theta)
{
    return 1.0f - cos(theta);
}

/**
 * @brief 玫瑰花极坐标函数 r = cos(3θ)
 */
float rose_polar_r(float theta)
{
    return cos(3 * theta);
}

/**
 * @brief 阿基米德螺旋极坐标函数 r = aθ
 */
float spiral_polar_r(float theta)
{
    return 0.1f * theta;
}

/**
 * @brief 测试第一个点的坐标计算
 * @param func 绘图函数结构体指针
 * @param params 绘图参数
 */
void test_first_point(draw_function_t *func, draw_params_t params)
{
    float x, y;
    float test_progress;

    // 设置测试进度
    if (func->func_xy != NULL)
    {
        test_progress = params.x_min;
        x = test_progress * params.scale;
        y = func->func_xy(test_progress) * params.scale;
    }
    else if (func->param_x != NULL && func->param_y != NULL)
    {
        test_progress = params.t_min;
        x = func->param_x(test_progress) * params.scale;
        y = func->param_y(test_progress) * params.scale;
    }
    else if (func->polar_r != NULL)
    {
        test_progress = params.theta_min;
        float r = func->polar_r(test_progress) * params.scale;
        x = r * cos(test_progress);
        y = r * sin(test_progress);
    }

    // 转换为脉冲坐标
    pulse_point_t pulse = convert_to_pulse(x, y);

    // 这里可以添加调试输出或直接移动测试
    // 例如：printf("First point: x=%.2f, y=%.2f, pulse_x=%ld, pulse_y=%ld\n", x, y, pulse.x, pulse.y);

    // 直接移动到第一个点进行测试
    move_to_point(x, y);
}
