/**
 * @file app_laser_draw.h
 * @brief 激光绘图系统
 * @copyright 白蛋电子工作室
 */

#ifndef __APP_LASER_DRAW_H_
#define __APP_LASER_DRAW_H_

#include "mydefine.h"
#include "app_motor.h"
#include <math.h>

/* 激光绘图配置宏定义 */
#define LASER_DRAW_LOOP_MODE 0     // 0:单次绘制 1:循环绘制
#define LASER_DRAW_STEP_DELAY 20   // 绘制步长延时(ms)
#define LASER_COORDINATE_SCALE 800 // 坐标缩放比例(脉冲/单位)

/* 纸张四角脉冲坐标定义(左上角开始顺时针) */
#define PAPER_TOP_LEFT_X -5000     // 左上角X脉冲
#define PAPER_TOP_LEFT_Y 5000      // 左上角Y脉冲
#define PAPER_TOP_RIGHT_X 5000     // 右上角X脉冲
#define PAPER_TOP_RIGHT_Y 5000     // 右上角Y脉冲
#define PAPER_BOTTOM_RIGHT_X 5000  // 右下角X脉冲
#define PAPER_BOTTOM_RIGHT_Y -5000 // 右下角Y脉冲
#define PAPER_BOTTOM_LEFT_X -5000  // 左下角X脉冲
#define PAPER_BOTTOM_LEFT_Y -5000  // 左下角Y脉冲

/* 纸张中心脉冲坐标(坐标系原点) */
#define PAPER_CENTER_X ((PAPER_TOP_LEFT_X + PAPER_BOTTOM_RIGHT_X) / 2)
#define PAPER_CENTER_Y ((PAPER_TOP_LEFT_Y + PAPER_BOTTOM_RIGHT_Y) / 2)

/* 激光控制IO定义 */
#define LASER_GPIO_PORT GPIOB     // 激光控制GPIO端口
#define LASER_GPIO_PIN GPIO_PIN_0 // 激光控制GPIO引脚

/* 绘图坐标点结构体 */
typedef struct
{
    float x; // X坐标(以纸张中心为原点)
    float y; // Y坐标(以纸张中心为原点)
} laser_point_t;

/* 脉冲坐标点结构体 */
typedef struct
{
    int32_t x; // X轴脉冲值
    int32_t y; // Y轴脉冲值
} pulse_point_t;

/* 绘图函数类型定义 */
typedef struct
{
    float (*func_xy)(float x);     // 直角坐标函数 y=f(x)
    float (*param_x)(float t);     // 参数方程X分量 x=f(t)
    float (*param_y)(float t);     // 参数方程Y分量 y=g(t)
    float (*polar_r)(float theta); // 极坐标函数 r=f(θ)
} draw_function_t;

/* 绘图参数结构体 */
typedef struct
{
    float x_min, x_max;         // X轴范围
    float y_min, y_max;         // Y轴范围
    float t_min, t_max;         // 参数t范围
    float theta_min, theta_max; // 角度θ范围
    float step;                 // 步长
    float scale;                // 缩放比例
} draw_params_t;

/* 绘图状态枚举 */
typedef enum
{
    LASER_DRAW_IDLE = 0, // 空闲状态
    LASER_DRAW_MOVING,   // 移动到起点
    LASER_DRAW_DRAWING,  // 正在绘制
    LASER_DRAW_FINISHED  // 绘制完成
} laser_draw_state_t;

/* 函数声明 */
void app_laser_draw_init(void);                                        // 激光绘图初始化
void laser_on(void);                                                   // 激光开启
void laser_off(void);                                                  // 激光关闭
pulse_point_t convert_to_pulse(float x, float y);                      // 坐标转换为脉冲
laser_point_t convert_to_coordinate(int32_t pulse_x, int32_t pulse_y); // 脉冲转换为坐标
void move_to_point(float x, float y);                                  // 移动到指定点(激光关)
void draw_to_point(float x, float y);                                  // 绘制到指定点(激光开)

/* 基本图形绘制函数 */
void draw_line(laser_point_t start, laser_point_t end);               // 绘制直线
void draw_rectangle(laser_point_t corner, float width, float height); // 绘制矩形
void draw_circle(laser_point_t center, float radius);                 // 绘制圆形

/* 绘图控制函数 */
void app_laser_draw_task(MultiTimer *timer, void *userData);        // 激光绘图任务函数
void start_drawing(draw_function_t *func, draw_params_t params);    // 开始绘制
void stop_drawing(void);                                            // 停止绘制
laser_draw_state_t get_draw_state(void);                            // 获取绘制状态
void test_first_point(draw_function_t *func, draw_params_t params); // 测试第一个点

/* 预定义数学函数 */
float line_function(float x);      // 直线函数
float sine_function(float x);      // 正弦函数
float cosine_function(float x);    // 余弦函数
float parabola_function(float x);  // 抛物线函数
float circle_param_x(float t);     // 圆参数方程X分量
float circle_param_y(float t);     // 圆参数方程Y分量
float heart_polar_r(float theta);  // 心形线极坐标函数
float rose_polar_r(float theta);   // 玫瑰花极坐标函数
float spiral_polar_r(float theta); // 螺旋线极坐标函数

#endif /* __APP_LASER_DRAW_H_ */
