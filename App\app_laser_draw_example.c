// Copyright (c) 2024 白蛋电子工作室

/**
 * @file app_laser_draw_example.c
 * @brief 激光绘图系统使用示例
 */

#include "app_laser_draw.h"

/**
 * @brief 激光绘图系统使用示例（非阻塞方式）
 */
void laser_draw_examples(void)
{
    // 初始化激光绘图系统
    app_laser_draw_init();

    /* 示例1: 绘制基本图形 */

    // 绘制直线
    laser_point_t line_start = {-2.0f, -1.0f};
    laser_point_t line_end = {2.0f, 1.0f};
    draw_line(line_start, line_end);

    // 绘制矩形
    laser_point_t rect_corner = {-1.5f, -1.0f};
    draw_rectangle(rect_corner, 3.0f, 2.0f);

    // 绘制圆形
    laser_point_t circle_center = {0.0f, 0.0f};
    draw_circle(circle_center, 1.5f);

    /* 示例2: 绘制数学函数图形（非阻塞方式） */

    // 绘制正弦波
    draw_function_t sine_func = {
        .func_xy = sine_function,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = NULL};

    draw_params_t sine_params = {
        .x_min = -3.14f,
        .x_max = 3.14f,
        .step = 0.1f,
        .scale = 1.0f};

    // 非阻塞启动绘制，函数立即返回，绘制在后台进行
    start_drawing(&sine_func, sine_params);

    // 绘制抛物线
    draw_function_t parabola_func = {
        .func_xy = parabola_function,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = NULL};

    draw_params_t parabola_params = {
        .x_min = -2.0f,
        .x_max = 2.0f,
        .step = 0.1f,
        .scale = 0.5f};

    start_drawing(&parabola_func, parabola_params);
    HAL_Delay(2000);

    /* 示例3: 绘制参数方程图形 */

    // 绘制圆形(参数方程)
    draw_function_t circle_param_func = {
        .func_xy = NULL,
        .param_x = circle_param_x,
        .param_y = circle_param_y,
        .polar_r = NULL};

    draw_params_t circle_param_params = {
        .t_min = 0.0f,
        .t_max = 6.28f, // 2π
        .step = 0.1f,
        .scale = 1.5f};

    start_drawing(&circle_param_func, circle_param_params);
    HAL_Delay(2000);

    /* 示例4: 绘制极坐标图形 */

    // 绘制心形线
    draw_function_t heart_func = {
        .func_xy = NULL,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = heart_polar_r};

    draw_params_t heart_params = {
        .theta_min = 0.0f,
        .theta_max = 6.28f, // 2π
        .step = 0.05f,
        .scale = 1.0f};

    start_drawing(&heart_func, heart_params);
    HAL_Delay(3000);

    // 绘制玫瑰花
    draw_function_t rose_func = {
        .func_xy = NULL,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = rose_polar_r};

    draw_params_t rose_params = {
        .theta_min = 0.0f,
        .theta_max = 6.28f, // 2π
        .step = 0.05f,
        .scale = 1.5f};

    start_drawing(&rose_func, rose_params);
    HAL_Delay(3000);

    // 绘制螺旋线
    draw_function_t spiral_func = {
        .func_xy = NULL,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = spiral_polar_r};

    draw_params_t spiral_params = {
        .theta_min = 0.0f,
        .theta_max = 12.56f, // 4π
        .step = 0.1f,
        .scale = 1.0f};

    start_drawing(&spiral_func, spiral_params);
    HAL_Delay(4000);
}

/**
 * @brief 自定义函数示例
 */

// 自定义波浪函数
float custom_wave_function(float x)
{
    return sin(x) + 0.5f * sin(3 * x);
}

// 自定义椭圆参数方程 - X分量
float ellipse_param_x(float t)
{
    return 2.0f * cos(t); // 长轴半径为2
}

// 自定义椭圆参数方程 - Y分量
float ellipse_param_y(float t)
{
    return 1.0f * sin(t); // 短轴半径为1
}

// 自定义花瓣极坐标函数
float custom_flower_polar_r(float theta)
{
    return 1.0f + 0.5f * cos(5 * theta);
}

/**
 * @brief 自定义图形绘制示例
 */
void custom_shapes_example(void)
{
    // 绘制自定义波浪
    draw_function_t wave_func = {
        .func_xy = custom_wave_function,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = NULL};

    draw_params_t wave_params = {
        .x_min = -6.28f,
        .x_max = 6.28f,
        .step = 0.1f,
        .scale = 1.0f};

    start_drawing(&wave_func, wave_params);
    HAL_Delay(3000);

    // 绘制椭圆
    draw_function_t ellipse_func = {
        .func_xy = NULL,
        .param_x = ellipse_param_x,
        .param_y = ellipse_param_y,
        .polar_r = NULL};

    draw_params_t ellipse_params = {
        .t_min = 0.0f,
        .t_max = 6.28f,
        .step = 0.1f,
        .scale = 1.0f};

    start_drawing(&ellipse_func, ellipse_params);
    HAL_Delay(2000);

    // 绘制自定义花瓣
    draw_function_t flower_func = {
        .func_xy = NULL,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = custom_flower_polar_r};

    draw_params_t flower_params = {
        .theta_min = 0.0f,
        .theta_max = 6.28f,
        .step = 0.05f,
        .scale = 1.2f};

    start_drawing(&flower_func, flower_params);
    HAL_Delay(3000);
}

/**
 * @brief 循环绘制示例(需要将LASER_DRAW_LOOP_MODE设置为1)
 */
void loop_drawing_example(void)
{
    // 注意：需要在app_laser_draw.h中将LASER_DRAW_LOOP_MODE设置为1

    draw_function_t sine_func = {
        .func_xy = sine_function,
        .param_x = NULL,
        .param_y = NULL,
        .polar_r = NULL};

    draw_params_t sine_params = {
        .x_min = -3.14f,
        .x_max = 3.14f,
        .step = 0.1f,
        .scale = 1.0f};

    // 开始循环绘制(会一直循环直到调用stop_drawing())
    start_drawing(&sine_func, sine_params);

    // 10秒后停止绘制
    HAL_Delay(10000);
    stop_drawing();
}
