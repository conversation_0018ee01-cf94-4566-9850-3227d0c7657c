// Copyright (c) 2024 ???????????

#ifndef __APP_MAIXCAM_H
#define __APP_MAIXCAM_H

#include "mydefine.h"

// ????潩?滰??
#define FRAME_HEADER '$'
#define FRAME_FOOTER '\n'
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'
#define BLUE_LASER_ID 'B'

// ?????????????
#ifndef LASERCOORD_T_DEFINED
#define LASERCOORD_T_DEFINED
typedef struct
{
    char type; // ????????: 'R'??????????'G'??????????
    int x;     // X????
    int y;     // Y????
} LaserCoord_t;
#endif

// ??????????????
typedef void (*LaserCoordCallback_t)(LaserCoord_t coord);

// MaixCam ??????
void maixcam_task(MultiTimer *timer, void *userData);

// ???????????
int maixcam_parse_data(char *buffer);

// ??????????????????
void maixcam_set_callback(LaserCoordCallback_t callback);

// ??????????????
void process_trajectory_command(char *cmd);

#endif /* __APP_MAIXCAM_H */
