/**
 * @file main.c
 * @brief 超简单电机快速测试 - 5分钟验证电机是否好用
 * @copyright 米醋电子工作室
 * @version 1.0
 * @date 2025-01-29
 * 
 * 使用方法：
 * 1. 编译烧录此程序到STM32F407
 * 2. 连接串口调试工具到UART1 (115200波特率)
 * 3. 观察电机运动 - 如果电机按顺序运动说明正常
 * 4. 串口输出测试结果
 */

#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "Emm_V5.h"
#include "MultiTimer.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

/* 简化配置 */
#define MOTOR_X_UART    huart4
#define MOTOR_Y_UART    huart5
#define DEBUG_UART      huart1

/* 兼容性变量定义 - 解决链接错误 */
MultiTimer mt_system, mt_oled, mt_usart, mt_cam, mt_pid, mt_user, mt_botton, mt_hmi, mt_polyline_trajectory;
uint8_t uart_flag = 0;

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Quick_Printf(const char* msg);
void Motor_Quick_Test(void);

/* 兼容性函数 - 解决链接错误 */
uint64_t bsp_get_systick(void)
{
  return (uint64_t)HAL_GetTick();
}

/**
 * @brief 简单打印函数
 */
void Quick_Printf(const char* msg)
{
    HAL_UART_Transmit(&DEBUG_UART, (uint8_t*)msg, strlen(msg), 1000);
}

/**
 * @brief 超简单电机测试 - 一键验证
 */
void Motor_Quick_Test(void)
{
    Quick_Printf("\r\n=== 电机快速测试开始 ===\r\n");
    
    // 1. 启用电机
    Quick_Printf("1. 启用电机...\r\n");
    Emm_V5_En_Control(&MOTOR_X_UART, 0x01, true, false);
    Emm_V5_En_Control(&MOTOR_Y_UART, 0x01, true, false);
    HAL_Delay(500);
    
    // 2. 测试X轴
    Quick_Printf("2. 测试X轴 (正向3秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, 0x01, 0, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, 0x01, false);
    HAL_Delay(1000);
    
    Quick_Printf("   X轴反向3秒...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, 0x01, 1, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, 0x01, false);
    HAL_Delay(1000);
    
    // 3. 测试Y轴
    Quick_Printf("3. 测试Y轴 (正向3秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_Y_UART, 0x01, 0, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, 0x01, false);
    HAL_Delay(1000);
    
    Quick_Printf("   Y轴反向3秒...\r\n");
    Emm_V5_Vel_Control(&MOTOR_Y_UART, 0x01, 1, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, 0x01, false);
    HAL_Delay(1000);
    
    // 4. 测试双轴
    Quick_Printf("4. 测试双轴联动 (3秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, 0x01, 0, 8, 0, false);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, 0x01, 0, 8, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, 0x01, false);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, 0x01, false);
    HAL_Delay(1000);
    
    // 5. 测试完成
    Quick_Printf("=== 测试完成 ===\r\n");
    Quick_Printf("如果电机按顺序运动，说明电机系统正常！\r\n");
    Quick_Printf("如果电机不动，请检查:\r\n");
    Quick_Printf("- 电机连接线 (UART4/UART5)\r\n");
    Quick_Printf("- 电机电源\r\n");
    Quick_Printf("- 电机地址设置\r\n\r\n");
}

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
    /* MCU Configuration--------------------------------------------------------*/
    
    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();
    
    /* Configure the system clock */
    SystemClock_Config();
    
    /* Initialize all configured peripherals */
    MX_GPIO_Init();
    MX_USART1_UART_Init();  // 调试串口
    MX_UART4_Init();        // X轴电机
    MX_UART5_Init();        // Y轴电机

    /* 初始化MultiTimer系统 - 兼容性需要 */
    multiTimerInstall(bsp_get_systick);

    /* 等待系统稳定 */
    HAL_Delay(1000);
    
    /* 执行快速测试 */
    Motor_Quick_Test();
    
    /* 主循环 - 每10秒重复测试 */
    while (1)
    {
        Quick_Printf("10秒后重复测试...\r\n");
        HAL_Delay(10000);
        Motor_Quick_Test();
    }
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
   */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
   * in the RCC_OscInitTypeDef structure.
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
