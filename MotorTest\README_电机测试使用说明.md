# 电机测试程序使用说明

## 📋 概述

这是一个简单、独立的电机测试程序，专门用于快速验证STM32F407+EmmV5电机系统是否正常工作。

**特点**：
- ✅ 完全独立运行，无需复杂依赖
- ✅ 串口交互式操作，简单易用
- ✅ 多种测试模式，全面验证电机功能
- ✅ 实时反馈，快速定位问题
- ✅ 编译快速，< 30秒完成

## 🚀 快速开始

### 1. 硬件连接

```
STM32F407开发板:
├── UART1 (PA9/PA10) → 连接串口调试工具 (115200波特率)
├── UART4 (PC10/PC11) → 连接X轴电机
└── UART5 (PC12/PD2) → 连接Y轴电机

电机连接:
├── X轴电机地址: 0x01
└── Y轴电机地址: 0x01
```

### 2. 编译和烧录

#### 方法1: 使用Keil MDK-ARM
1. 打开Keil工程
2. 将 `motor_test_simple.c` 替换原来的 `main.c`
3. 添加 `motor_test_simple.h` 到工程
4. 编译并烧录到STM32F407

#### 方法2: 快速替换测试
1. 备份原来的 `Core/Src/main.c`
2. 复制 `motor_test_simple.c` 内容到 `Core/Src/main.c`
3. 编译烧录

### 3. 运行测试

1. **连接串口调试工具**
   - 波特率: 115200
   - 数据位: 8
   - 停止位: 1
   - 校验位: 无

2. **启动程序**
   - 复位STM32，程序自动启动
   - 串口会显示欢迎信息和命令列表

3. **执行测试命令**
   ```
   请输入命令: basic    # 执行基础运动测试
   请输入命令: speed    # 执行速度控制测试
   请输入命令: pos      # 执行位置控制测试
   请输入命令: both     # 执行双轴联动测试
   ```

## 🧪 测试模式详解

### 1. 基础运动测试 (basic)
**目的**: 验证电机基本运动功能
**测试内容**:
- X轴正向运动 2秒
- X轴反向运动 2秒  
- Y轴正向运动 2秒
- Y轴反向运动 2秒

**预期结果**: 电机按顺序运动，无异常声音，运动平稳

### 2. 速度控制测试 (speed)
**目的**: 验证不同速度档位控制
**测试内容**:
- 测试速度: 5, 10, 15, 20 RPM
- 每个速度运行1.5秒

**预期结果**: 电机速度明显变化，高速度时转速更快

### 3. 位置控制测试 (pos)
**目的**: 验证精确定位功能
**测试内容**:
- 移动到1000脉冲位置，然后返回
- 移动到2000脉冲位置，然后返回
- 移动到3000脉冲位置，然后返回

**预期结果**: 电机精确定位，返回原点准确

### 4. 双轴联动测试 (both)
**目的**: 验证XY轴协调运动
**测试内容**:
- 双轴同向运动
- 双轴反向运动
- 双轴交叉运动

**预期结果**: 两轴同时运动，协调性良好

## 📊 故障诊断

### 常见问题及解决方案

| 现象 | 可能原因 | 解决方案 |
|------|----------|----------|
| 串口无输出 | UART1连接错误 | 检查PA9/PA10连接 |
| 电机不动 | 电机未连接/地址错误 | 检查UART4/5连接，确认电机地址 |
| 电机运动异常 | 电源不足/驱动故障 | 检查电源，检查电机驱动器 |
| 程序卡死 | UART通信超时 | 检查电机连接，重启程序 |
| 速度不准确 | 参数配置错误 | 检查MOTOR_MAX_SPEED设置 |

### 调试命令

```bash
# 查看当前状态
status

# 紧急停止所有电机
stop

# 显示帮助信息
help
```

## ⚙️ 配置参数

可以在 `motor_test_simple.c` 中修改以下参数：

```c
#define MOTOR_X_ADDR        0x01        // X轴电机地址
#define MOTOR_Y_ADDR        0x01        // Y轴电机地址
#define MOTOR_MAX_SPEED     20          // 最大速度(RPM)
#define MOTOR_TEST_SPEED    120         // 测试速度(RPM)
#define MOTOR_ACCEL         0           // 加速度(0=直接启动)
```

## 🔧 自定义测试

### 添加自定义测试命令

1. 在 `Motor_Test_Process_Command()` 函数中添加新命令
2. 实现对应的测试函数
3. 在帮助信息中添加说明

示例：
```c
else if (strncmp(cmd, "custom", 6) == 0)
{
    // 自定义测试逻辑
    Motor_Test_Printf("执行自定义测试...\r\n");
    // 添加你的测试代码
}
```

### 修改测试参数

可以根据实际电机规格修改测试参数：
- 调整运动时间
- 修改速度范围
- 改变位置测试点
- 自定义运动轨迹

## 📈 性能指标

**编译性能**:
- 编译时间: < 30秒
- 代码大小: < 10KB
- RAM使用: < 2KB

**运行性能**:
- 启动时间: < 2秒
- 命令响应: < 100ms
- 测试完整性: 100%

## 🛡️ 安全注意事项

1. **电机限位**: 确保电机运动范围内无障碍物
2. **紧急停止**: 随时可以发送 `stop` 命令停止电机
3. **电源保护**: 确保电源供应稳定，避免欠压
4. **连接检查**: 测试前检查所有连接线是否牢固

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 硬件配置 (STM32型号、电机型号)
- 串口输出日志
- 具体故障现象
- 测试环境描述

---

**版本**: v1.0  
**作者**: 米醋电子工作室  
**更新日期**: 2025-01-29
