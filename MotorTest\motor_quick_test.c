/**
 * @file motor_quick_test.c
 * @brief 超简单电机快速测试 - 5分钟验证电机是否好用
 * @copyright 米醋电子工作室
 * @version 1.0
 * @date 2025-01-29
 * 
 * 使用方法：
 * 1. 替换main.c内容为此文件
 * 2. 编译烧录
 * 3. 观察电机运动 - 如果电机按顺序运动说明正常
 * 4. 串口输出测试结果 (115200波特率)
 */

#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "Emm_V5.h"
#include <stdio.h>

/* 简化配置 */
#define MOTOR_X_UART    huart4
#define MOTOR_Y_UART    huart5
#define DEBUG_UART      huart1

/**
 * @brief 简单打印函数
 */
void Quick_Printf(const char* msg)
{
    HAL_UART_Transmit(&DEBUG_UART, (uint8_t*)msg, strlen(msg), 1000);
}

/**
 * @brief 超简单电机测试 - 一键验证
 */
void Motor_Quick_Test(void)
{
    Quick_Printf("\r\n=== 电机快速测试开始 ===\r\n");
    
    // 1. 启用电机
    Quick_Printf("1. 启用电机...\r\n");
    Emm_V5_En_Control(&MOTOR_X_UART, 0x01, true, false);
    Emm_V5_En_Control(&MOTOR_Y_UART, 0x01, true, false);
    HAL_Delay(500);
    
    // 2. 测试X轴
    Quick_Printf("2. 测试X轴 (正向3秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, 0x01, 0, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, 0x01, false);
    HAL_Delay(1000);
    
    Quick_Printf("   X轴反向3秒...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, 0x01, 1, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, 0x01, false);
    HAL_Delay(1000);
    
    // 3. 测试Y轴
    Quick_Printf("3. 测试Y轴 (正向3秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_Y_UART, 0x01, 0, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, 0x01, false);
    HAL_Delay(1000);
    
    Quick_Printf("   Y轴反向3秒...\r\n");
    Emm_V5_Vel_Control(&MOTOR_Y_UART, 0x01, 1, 10, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, 0x01, false);
    HAL_Delay(1000);
    
    // 4. 测试双轴
    Quick_Printf("4. 测试双轴联动 (3秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, 0x01, 0, 8, 0, false);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, 0x01, 0, 8, 0, false);
    HAL_Delay(3000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, 0x01, false);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, 0x01, false);
    HAL_Delay(1000);
    
    // 5. 测试完成
    Quick_Printf("=== 测试完成 ===\r\n");
    Quick_Printf("如果电机按顺序运动，说明电机系统正常！\r\n");
    Quick_Printf("如果电机不动，请检查:\r\n");
    Quick_Printf("- 电机连接线 (UART4/UART5)\r\n");
    Quick_Printf("- 电机电源\r\n");
    Quick_Printf("- 电机地址设置\r\n\r\n");
}

/**
 * @brief 主函数
 */
int main(void)
{
    /* 系统初始化 */
    HAL_Init();
    SystemClock_Config();
    
    /* 外设初始化 */
    MX_GPIO_Init();
    MX_USART1_UART_Init();  // 调试串口
    MX_UART4_Init();        // X轴电机
    MX_UART5_Init();        // Y轴电机
    
    /* 等待系统稳定 */
    HAL_Delay(1000);
    
    /* 执行快速测试 */
    Motor_Quick_Test();
    
    /* 主循环 - 每10秒重复测试 */
    while (1)
    {
        Quick_Printf("10秒后重复测试...\r\n");
        HAL_Delay(10000);
        Motor_Quick_Test();
    }
}
