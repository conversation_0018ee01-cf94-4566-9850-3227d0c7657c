/**
 * @file motor_test_simple.c
 * @brief 简单电机测试程序 - 独立运行，快速验证电机功能
 * @copyright 米醋电子工作室
 * @version 1.0
 * @date 2025-01-29
 * 
 * 使用说明：
 * 1. 编译并烧录此程序到STM32F407
 * 2. 连接串口调试工具到UART1 (115200波特率)
 * 3. 发送测试命令验证电机功能
 * 4. 观察电机运动和串口反馈
 */

#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "Emm_V5.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* 电机配置参数 */
#define MOTOR_X_ADDR        0x01        // X轴电机地址
#define MOTOR_Y_ADDR        0x01        // Y轴电机地址  
#define MOTOR_X_UART        huart4      // X轴电机串口
#define MOTOR_Y_UART        huart5      // Y轴电机串口
#define DEBUG_UART          huart1      // 调试串口
#define MOTOR_MAX_SPEED     20          // 最大速度(RPM)
#define MOTOR_TEST_SPEED    120         // 测试速度(RPM)
#define MOTOR_ACCEL         0           // 加速度(0=直接启动)

/* 测试状态 */
typedef enum {
    TEST_IDLE = 0,
    TEST_RUNNING,
    TEST_COMPLETED,
    TEST_ERROR
} test_status_t;

/* 全局变量 */
static test_status_t g_test_status = TEST_IDLE;
static char g_rx_buffer[128];
static uint8_t g_rx_index = 0;
static uint32_t g_test_start_time = 0;

/* 函数声明 */
void Motor_Test_Init(void);
void Motor_Test_Basic_Movement(void);
void Motor_Test_Speed_Control(void);
void Motor_Test_Position_Control(void);
void Motor_Test_Both_Axis(void);
void Motor_Test_Stop_All(void);
void Motor_Test_Print_Help(void);
void Motor_Test_Process_Command(const char* cmd);
int Motor_Test_Printf(const char* format, ...);

/**
 * @brief 电机测试初始化
 */
void Motor_Test_Init(void)
{
    Motor_Test_Printf("\r\n=== 电机测试程序启动 ===\r\n");
    Motor_Test_Printf("版本: v1.0\r\n");
    Motor_Test_Printf("日期: 2025-01-29\r\n");
    Motor_Test_Printf("作者: 米醋电子工作室\r\n\r\n");
    
    // 启用电机
    Motor_Test_Printf("正在初始化电机...\r\n");
    
    // 启用X轴电机
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, false);
    HAL_Delay(100);
    
    // 启用Y轴电机  
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, false);
    HAL_Delay(100);
    
    // 停止所有电机
    Motor_Test_Stop_All();
    HAL_Delay(100);
    
    Motor_Test_Printf("电机初始化完成！\r\n\r\n");
    Motor_Test_Print_Help();
    
    g_test_status = TEST_IDLE;
}

/**
 * @brief 基础运动测试 - 简单的前后左右运动
 */
void Motor_Test_Basic_Movement(void)
{
    Motor_Test_Printf("开始基础运动测试...\r\n");
    g_test_status = TEST_RUNNING;
    g_test_start_time = HAL_GetTick();
    
    // 测试X轴正向
    Motor_Test_Printf("1. X轴正向运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 0, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, false);
    HAL_Delay(500);
    
    // 测试X轴反向
    Motor_Test_Printf("2. X轴反向运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 1, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, false);
    HAL_Delay(500);
    
    // 测试Y轴正向
    Motor_Test_Printf("3. Y轴正向运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, 0, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, false);
    HAL_Delay(500);
    
    // 测试Y轴反向
    Motor_Test_Printf("4. Y轴反向运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, 1, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, false);
    HAL_Delay(500);
    
    uint32_t test_time = HAL_GetTick() - g_test_start_time;
    Motor_Test_Printf("基础运动测试完成！耗时: %lu ms\r\n\r\n", test_time);
    g_test_status = TEST_COMPLETED;
}

/**
 * @brief 速度控制测试 - 测试不同速度
 */
void Motor_Test_Speed_Control(void)
{
    Motor_Test_Printf("开始速度控制测试...\r\n");
    g_test_status = TEST_RUNNING;
    
    uint16_t speeds[] = {5, 10, 15, 20}; // 不同速度档位
    uint8_t speed_count = sizeof(speeds) / sizeof(speeds[0]);
    
    for (uint8_t i = 0; i < speed_count; i++)
    {
        Motor_Test_Printf("测试速度: %d RPM (X轴正向, 1.5秒)\r\n", speeds[i]);
        Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 0, speeds[i], MOTOR_ACCEL, false);
        HAL_Delay(1500);
        Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, false);
        HAL_Delay(300);
    }
    
    Motor_Test_Printf("速度控制测试完成！\r\n\r\n");
    g_test_status = TEST_COMPLETED;
}

/**
 * @brief 位置控制测试 - 测试精确定位
 */
void Motor_Test_Position_Control(void)
{
    Motor_Test_Printf("开始位置控制测试...\r\n");
    g_test_status = TEST_RUNNING;
    
    uint32_t positions[] = {1000, 2000, 3000}; // 不同位置
    uint8_t pos_count = sizeof(positions) / sizeof(positions[0]);
    
    for (uint8_t i = 0; i < pos_count; i++)
    {
        Motor_Test_Printf("移动到位置: %lu 脉冲 (X轴)...\r\n", positions[i]);
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 1, MOTOR_TEST_SPEED, MOTOR_ACCEL, positions[i], true, false);
        HAL_Delay(3000); // 等待运动完成
        
        Motor_Test_Printf("返回原点...\r\n");
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 0, MOTOR_TEST_SPEED, MOTOR_ACCEL, positions[i], true, false);
        HAL_Delay(3000); // 等待运动完成
    }
    
    Motor_Test_Printf("位置控制测试完成！\r\n\r\n");
    g_test_status = TEST_COMPLETED;
}

/**
 * @brief 双轴联动测试 - 测试XY轴同时运动
 */
void Motor_Test_Both_Axis(void)
{
    Motor_Test_Printf("开始双轴联动测试...\r\n");
    g_test_status = TEST_RUNNING;
    
    // 测试1: 同向运动
    Motor_Test_Printf("1. 双轴同向运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 0, 10, MOTOR_ACCEL, false);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, 0, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Motor_Test_Stop_All();
    HAL_Delay(500);
    
    // 测试2: 反向运动
    Motor_Test_Printf("2. 双轴反向运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 1, 10, MOTOR_ACCEL, false);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, 1, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Motor_Test_Stop_All();
    HAL_Delay(500);
    
    // 测试3: 交叉运动
    Motor_Test_Printf("3. 双轴交叉运动 (2秒)...\r\n");
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, 0, 10, MOTOR_ACCEL, false);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, 1, 10, MOTOR_ACCEL, false);
    HAL_Delay(2000);
    Motor_Test_Stop_All();
    HAL_Delay(500);
    
    Motor_Test_Printf("双轴联动测试完成！\r\n\r\n");
    g_test_status = TEST_COMPLETED;
}

/**
 * @brief 停止所有电机
 */
void Motor_Test_Stop_All(void)
{
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, false);
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, false);
    Motor_Test_Printf("所有电机已停止\r\n");
}

/**
 * @brief 打印帮助信息
 */
void Motor_Test_Print_Help(void)
{
    Motor_Test_Printf("=== 电机测试命令列表 ===\r\n");
    Motor_Test_Printf("1. basic    - 基础运动测试 (前后左右)\r\n");
    Motor_Test_Printf("2. speed    - 速度控制测试 (不同速度档位)\r\n");
    Motor_Test_Printf("3. pos      - 位置控制测试 (精确定位)\r\n");
    Motor_Test_Printf("4. both     - 双轴联动测试 (XY轴同时运动)\r\n");
    Motor_Test_Printf("5. stop     - 停止所有电机\r\n");
    Motor_Test_Printf("6. help     - 显示此帮助信息\r\n");
    Motor_Test_Printf("7. status   - 显示当前状态\r\n");
    Motor_Test_Printf("\r\n请输入命令: ");
}

/**
 * @brief 处理串口命令
 */
void Motor_Test_Process_Command(const char* cmd)
{
    if (strncmp(cmd, "basic", 5) == 0)
    {
        Motor_Test_Basic_Movement();
    }
    else if (strncmp(cmd, "speed", 5) == 0)
    {
        Motor_Test_Speed_Control();
    }
    else if (strncmp(cmd, "pos", 3) == 0)
    {
        Motor_Test_Position_Control();
    }
    else if (strncmp(cmd, "both", 4) == 0)
    {
        Motor_Test_Both_Axis();
    }
    else if (strncmp(cmd, "stop", 4) == 0)
    {
        Motor_Test_Stop_All();
    }
    else if (strncmp(cmd, "help", 4) == 0)
    {
        Motor_Test_Print_Help();
    }
    else if (strncmp(cmd, "status", 6) == 0)
    {
        const char* status_str[] = {"空闲", "运行中", "已完成", "错误"};
        Motor_Test_Printf("当前状态: %s\r\n", status_str[g_test_status]);
        Motor_Test_Printf("系统运行时间: %lu ms\r\n", HAL_GetTick());
    }
    else
    {
        Motor_Test_Printf("未知命令: %s\r\n", cmd);
        Motor_Test_Printf("输入 'help' 查看可用命令\r\n");
    }
    
    Motor_Test_Printf("\r\n请输入命令: ");
}

/**
 * @brief 格式化打印函数
 */
int Motor_Test_Printf(const char* format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    HAL_UART_Transmit(&DEBUG_UART, (uint8_t*)buffer, len, 1000);
    return len;
}

/**
 * @brief 串口接收回调函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        uint8_t received_char;
        HAL_UART_Receive(&DEBUG_UART, &received_char, 1, 0);
        
        if (received_char == '\r' || received_char == '\n')
        {
            if (g_rx_index > 0)
            {
                g_rx_buffer[g_rx_index] = '\0';
                Motor_Test_Process_Command(g_rx_buffer);
                g_rx_index = 0;
            }
        }
        else if (g_rx_index < sizeof(g_rx_buffer) - 1)
        {
            g_rx_buffer[g_rx_index++] = received_char;
            // 回显字符
            HAL_UART_Transmit(&DEBUG_UART, &received_char, 1, 100);
        }
        
        // 重新启动接收
        HAL_UART_Receive_IT(&DEBUG_UART, &received_char, 1);
    }
}

/**
 * @brief 主函数 - 电机测试程序入口
 */
int main(void)
{
    /* 系统初始化 */
    HAL_Init();
    SystemClock_Config();
    
    /* 外设初始化 */
    MX_GPIO_Init();
    MX_USART1_UART_Init();  // 调试串口
    MX_UART4_Init();        // X轴电机串口
    MX_UART5_Init();        // Y轴电机串口
    
    /* 电机测试初始化 */
    Motor_Test_Init();
    
    /* 启动串口接收中断 */
    uint8_t dummy;
    HAL_UART_Receive_IT(&DEBUG_UART, &dummy, 1);
    
    /* 主循环 */
    while (1)
    {
        HAL_Delay(100);
        
        // 可以在这里添加定期状态检查
        // 例如：检查电机通信状态、监控系统运行时间等
    }
}
