/**
 * @file motor_test_simple.h
 * @brief 简单电机测试程序头文件
 * @copyright 米醋电子工作室
 * @version 1.0
 * @date 2025-01-29
 */

#ifndef __MOTOR_TEST_SIMPLE_H__
#define __MOTOR_TEST_SIMPLE_H__

#include "main.h"
#include "usart.h"
#include <stdint.h>
#include <stdbool.h>

/* 测试状态枚举 */
typedef enum {
    TEST_IDLE = 0,      // 空闲状态
    TEST_RUNNING,       // 测试运行中
    TEST_COMPLETED,     // 测试完成
    TEST_ERROR          // 测试错误
} test_status_t;

/* 电机配置宏定义 */
#define MOTOR_X_ADDR        0x01        // X轴电机地址
#define MOTOR_Y_ADDR        0x01        // Y轴电机地址  
#define MOTOR_X_UART        huart4      // X轴电机串口
#define MOTOR_Y_UART        huart5      // Y轴电机串口
#define DEBUG_UART          huart1      // 调试串口
#define MOTOR_MAX_SPEED     20          // 最大速度(RPM)
#define MOTOR_TEST_SPEED    120         // 测试速度(RPM)
#define MOTOR_ACCEL         0           // 加速度(0=直接启动)

/* 函数声明 */
void Motor_Test_Init(void);
void Motor_Test_Basic_Movement(void);
void Motor_Test_Speed_Control(void);
void Motor_Test_Position_Control(void);
void Motor_Test_Both_Axis(void);
void Motor_Test_Stop_All(void);
void Motor_Test_Print_Help(void);
void Motor_Test_Process_Command(const char* cmd);
int Motor_Test_Printf(const char* format, ...);

#endif /* __MOTOR_TEST_SIMPLE_H__ */
