@echo off
chcp 65001 >nul
echo ========================================
echo 电机快速测试部署脚本
echo 版本: v1.0
echo 作者: 米醋电子工作室
echo ========================================
echo.

echo 请选择测试模式:
echo 1. 超简单测试 (自动运行，观察电机运动)
echo 2. 交互式测试 (串口命令控制)
echo 3. 恢复原始main.c
echo.

set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" goto SIMPLE_TEST
if "%choice%"=="2" goto INTERACTIVE_TEST  
if "%choice%"=="3" goto RESTORE_MAIN
goto INVALID_CHOICE

:SIMPLE_TEST
echo.
echo 正在部署超简单测试...
echo 1. 备份原始main.c
if exist "..\Core\Src\main.c" (
    copy "..\Core\Src\main.c" "..\Core\Src\main_backup.c" >nul
    echo    ✓ 已备份 main.c -> main_backup.c
) else (
    echo    ✗ 未找到原始main.c文件
    pause
    exit /b 1
)

echo 2. 复制快速测试代码
copy "motor_quick_test.c" "..\Core\Src\main.c" >nul
echo    ✓ 已部署快速测试代码

echo 3. 使用说明:
echo    - 编译并烧录程序到STM32F407
echo    - 连接串口调试工具 (115200波特率)
echo    - 观察电机运动和串口输出
echo    - 如果电机按顺序运动说明正常
echo.
echo 部署完成！请编译烧录程序。
goto END

:INTERACTIVE_TEST
echo.
echo 正在部署交互式测试...
echo 1. 备份原始main.c
if exist "..\Core\Src\main.c" (
    copy "..\Core\Src\main.c" "..\Core\Src\main_backup.c" >nul
    echo    ✓ 已备份 main.c -> main_backup.c
) else (
    echo    ✗ 未找到原始main.c文件
    pause
    exit /b 1
)

echo 2. 复制交互式测试代码
copy "motor_test_simple.c" "..\Core\Src\main.c" >nul
echo    ✓ 已部署交互式测试代码

echo 3. 使用说明:
echo    - 编译并烧录程序到STM32F407
echo    - 连接串口调试工具 (115200波特率)
echo    - 发送测试命令: basic, speed, pos, both, stop, help
echo    - 根据串口提示进行测试
echo.
echo 部署完成！请编译烧录程序。
goto END

:RESTORE_MAIN
echo.
echo 正在恢复原始main.c...
if exist "..\Core\Src\main_backup.c" (
    copy "..\Core\Src\main_backup.c" "..\Core\Src\main.c" >nul
    echo    ✓ 已恢复原始main.c
    del "..\Core\Src\main_backup.c" >nul
    echo    ✓ 已删除备份文件
) else (
    echo    ✗ 未找到备份文件 main_backup.c
    echo    请手动恢复main.c文件
)
echo.
echo 恢复完成！
goto END

:INVALID_CHOICE
echo.
echo 无效选择，请重新运行脚本。
goto END

:END
echo.
echo ========================================
echo 脚本执行完成
echo ========================================
pause
