# 独立电机控制模块架构设计文档

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-29 |
| 负责人 | Bob (架构师) |
| 项目名称 | 独立电机控制模块架构设计 |
| 版权归属 | 米醋电子工作室 |

### 版本历史
- v1.0 (2025-01-29): 初始架构设计，定义模块结构和技术方案

## 2. 架构概述

### 2.1 设计原则
1. **最小依赖**: 仅依赖必要的HAL库和EmmV5驱动
2. **高内聚低耦合**: 模块内部功能高度内聚，对外接口简洁
3. **可测试性**: 每个模块都可独立测试
4. **可扩展性**: 预留扩展接口，便于功能增强
5. **资源优化**: 最小化内存和Flash占用

### 2.2 核心目标
- **编译时间**: < 30秒 (vs 原项目 > 2分钟)
- **内存占用**: < 20KB RAM (vs 原项目 > 100KB)
- **启动时间**: < 3秒 (vs 原项目 > 10秒)
- **响应延迟**: < 10ms
- **代码复用**: > 80%

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    独立电机控制模块                          │
├─────────────────────────────────────────────────────────────┤
│  Debug Interface Layer (调试接口层)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ UART Debug  │ │ Command     │ │ Status      │           │
│  │ Interface   │ │ Parser      │ │ Monitor     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Application Layer (应用层)                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Test        │ │ Trajectory  │ │ File        │           │
│  │ Patterns    │ │ Generator   │ │ Manager     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Core Control Layer (核心控制层)                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Motor       │ │ Config      │ │ Error       │           │
│  │ Control     │ │ Manager     │ │ Handler     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  Hardware Abstraction Layer (硬件抽象层)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ EmmV5       │ │ UART        │ │ Timer       │           │
│  │ Driver      │ │ Interface   │ │ Service     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  STM32 HAL Layer (STM32硬件抽象层)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ UART HAL    │ │ GPIO HAL    │ │ Timer HAL   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 模块依赖关系图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Debug Layer   │    │  Application    │    │   Test Layer    │
│                 │    │     Layer       │    │                 │
│ • debug_cmd.h   │    │ • test_traj.h   │    │ • test_utils.h  │
│ • debug_mon.h   │    │ • file_mgr.h    │    │ • test_patt.h   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────────┐
                    │      Core Layer             │
                    │                             │
                    │ • motor_control.h           │
                    │ • motor_config.h            │
                    │ • motor_types.h             │
                    │ • error_handler.h           │
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │   Hardware Layer            │
                    │                             │
                    │ • emm_v5_driver.h           │
                    │ • uart_interface.h          │
                    │ • timer_service.h           │
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │     STM32 HAL               │
                    │                             │
                    │ • stm32f4xx_hal_uart.h      │
                    │ • stm32f4xx_hal_gpio.h      │
                    │ • stm32f4xx_hal_tim.h       │
                    └─────────────────────────────┘
```

## 4. 详细模块设计

### 4.1 目录结构设计

```
MotorDebugModule/
├── Core/                           # 核心控制层
│   ├── Inc/                        # 头文件
│   │   ├── motor_control.h         # 电机控制核心接口
│   │   ├── motor_config.h          # 电机配置管理
│   │   ├── motor_types.h           # 数据类型定义
│   │   └── error_handler.h         # 错误处理
│   └── Src/                        # 源文件
│       ├── motor_control.c         # 电机控制实现
│       ├── motor_config.c          # 配置管理实现
│       └── error_handler.c         # 错误处理实现
├── Debug/                          # 调试功能层
│   ├── Inc/
│   │   ├── debug_interface.h       # 调试接口定义
│   │   ├── debug_commands.h        # 命令解析器
│   │   ├── debug_monitor.h         # 状态监控
│   │   └── debug_logger.h          # 日志记录
│   └── Src/
│       ├── debug_interface.c       # 调试接口实现
│       ├── debug_commands.c        # 命令解析实现
│       ├── debug_monitor.c         # 状态监控实现
│       └── debug_logger.c          # 日志记录实现
├── Test/                           # 测试功能层
│   ├── Inc/
│   │   ├── test_trajectory.h       # 测试轨迹生成
│   │   ├── test_patterns.h         # 测试模式
│   │   └── test_utils.h            # 测试工具
│   └── Src/
│       ├── test_trajectory.c       # 轨迹生成实现
│       ├── test_patterns.c         # 测试模式实现
│       └── test_utils.c            # 测试工具实现
├── Utils/                          # 工具模块
│   ├── Inc/
│   │   ├── file_manager.h          # 文件管理
│   │   ├── string_utils.h          # 字符串工具
│   │   ├── math_utils.h            # 数学工具
│   │   └── simple_timer.h          # 简化定时器
│   └── Src/
│       ├── file_manager.c          # 文件管理实现
│       ├── string_utils.c          # 字符串工具实现
│       ├── math_utils.c            # 数学工具实现
│       └── simple_timer.c          # 简化定时器实现
├── Drivers/                        # 驱动层
│   ├── Inc/
│   │   ├── emm_v5_driver.h         # EmmV5驱动接口
│   │   ├── uart_interface.h        # UART接口封装
│   │   └── timer_service.h         # 定时器服务
│   └── Src/
│       ├── emm_v5_driver.c         # EmmV5驱动实现
│       ├── uart_interface.c        # UART接口实现
│       └── timer_service.c         # 定时器服务实现
├── Config/                         # 配置文件
│   ├── motor_config.h              # 电机配置参数
│   ├── debug_config.h              # 调试配置参数
│   ├── system_config.h             # 系统配置参数
│   └── build_config.h              # 编译配置参数
├── Examples/                       # 示例程序
│   ├── basic_control_example.c     # 基础控制示例
│   ├── debug_interface_example.c   # 调试接口示例
│   └── test_trajectory_example.c   # 测试轨迹示例
├── Scripts/                        # 脚本工具
│   ├── build.bat                   # 编译脚本
│   ├── clean_debug.bat             # 调试文件清理脚本
│   └── flash.bat                   # 烧录脚本
├── Docs/                           # 文档目录
│   ├── API_Reference.md            # API参考文档
│   ├── User_Manual.md              # 用户手册
│   └── Quick_Start.md              # 快速开始指南
└── Tests/                          # 测试目录
    ├── unit_tests/                 # 单元测试
    ├── integration_tests/          # 集成测试
    └── performance_tests/          # 性能测试
```

### 4.2 核心数据结构设计

#### 4.2.1 电机控制数据结构 (motor_types.h)

```c
// 电机轴枚举
typedef enum {
    MOTOR_AXIS_X = 0,
    MOTOR_AXIS_Y = 1,
    MOTOR_AXIS_COUNT = 2
} motor_axis_t;

// 电机状态枚举
typedef enum {
    MOTOR_STATE_IDLE = 0,       // 空闲状态
    MOTOR_STATE_RUNNING = 1,    // 运行状态
    MOTOR_STATE_ERROR = 2,      // 错误状态
    MOTOR_STATE_INIT = 3        // 初始化状态
} motor_state_t;

// 电机控制模式枚举
typedef enum {
    MOTOR_MODE_SPEED = 0,       // 速度控制模式
    MOTOR_MODE_POSITION = 1     // 位置控制模式
} motor_mode_t;

// 电机配置结构体
typedef struct {
    uint8_t address;            // 电机地址
    UART_HandleTypeDef* uart;   // 串口句柄
    uint16_t max_speed;         // 最大速度(RPM)
    uint16_t pulse_speed;       // 脉冲速度(RPM)
    uint8_t acceleration;       // 加速度
    bool sync_flag;             // 同步标志
    int32_t max_position;       // 最大位置限制
    int32_t min_position;       // 最小位置限制
} motor_config_t;

// 电机状态结构体
typedef struct {
    motor_state_t state;        // 当前状态
    motor_mode_t mode;          // 控制模式
    int32_t current_position;   // 当前位置(脉冲)
    int16_t current_speed;      // 当前速度(RPM)
    int8_t target_speed_percent; // 目标速度百分比
    int32_t target_position;    // 目标位置(脉冲)
    uint32_t error_code;        // 错误代码
    uint32_t last_update_time;  // 最后更新时间
} motor_status_t;

// 双轴电机系统结构体
typedef struct {
    motor_config_t config[MOTOR_AXIS_COUNT];  // 电机配置
    motor_status_t status[MOTOR_AXIS_COUNT];  // 电机状态
    bool system_initialized;                 // 系统初始化标志
    uint32_t system_uptime;                  // 系统运行时间
    uint32_t command_count;                  // 命令计数
} motor_system_t;
```

#### 4.2.2 调试接口数据结构 (debug_interface.h)

```c
// 调试命令类型枚举
typedef enum {
    DEBUG_CMD_MOTOR_INIT = 0,
    DEBUG_CMD_MOTOR_SPEED,
    DEBUG_CMD_MOTOR_POS,
    DEBUG_CMD_MOTOR_STOP,
    DEBUG_CMD_MOTOR_STATUS,
    DEBUG_CMD_MOTOR_SET_PARAM,
    DEBUG_CMD_MOTOR_GET_PARAM,
    DEBUG_CMD_TEST_CIRCLE,
    DEBUG_CMD_TEST_SQUARE,
    DEBUG_CMD_DEBUG_ON,
    DEBUG_CMD_DEBUG_OFF,
    DEBUG_CMD_UNKNOWN
} debug_cmd_type_t;

// 调试命令结构体
typedef struct {
    debug_cmd_type_t type;      // 命令类型
    int32_t params[4];          // 命令参数(最多4个)
    uint8_t param_count;        // 参数个数
    char raw_command[64];       // 原始命令字符串
} debug_command_t;

// 调试状态结构体
typedef struct {
    bool debug_enabled;         // 调试模式开关
    bool monitor_enabled;       // 监控开关
    uint32_t monitor_interval;  // 监控间隔(ms)
    uint32_t last_monitor_time; // 最后监控时间
    uint32_t command_count;     // 命令计数
    uint32_t error_count;       // 错误计数
} debug_status_t;
```

## 5. 接口设计

### 5.1 核心电机控制接口 (motor_control.h)

```c
// 系统初始化和配置
HAL_StatusTypeDef Motor_System_Init(void);
HAL_StatusTypeDef Motor_System_DeInit(void);
HAL_StatusTypeDef Motor_Set_Config(motor_axis_t axis, const motor_config_t* config);
HAL_StatusTypeDef Motor_Get_Config(motor_axis_t axis, motor_config_t* config);

// 基础控制接口
HAL_StatusTypeDef Motor_Set_Speed(motor_axis_t axis, int8_t speed_percent);
HAL_StatusTypeDef Motor_Set_Position(motor_axis_t axis, int32_t position);
HAL_StatusTypeDef Motor_Stop(motor_axis_t axis);
HAL_StatusTypeDef Motor_Stop_All(void);

// 状态查询接口
HAL_StatusTypeDef Motor_Get_Status(motor_axis_t axis, motor_status_t* status);
HAL_StatusTypeDef Motor_Get_Position(motor_axis_t axis, int32_t* position);
HAL_StatusTypeDef Motor_Get_Speed(motor_axis_t axis, int16_t* speed);
motor_state_t Motor_Get_State(motor_axis_t axis);

// 双轴联合控制接口
HAL_StatusTypeDef Motor_Set_Speed_XY(int8_t x_percent, int8_t y_percent);
HAL_StatusTypeDef Motor_Set_Position_XY(int32_t x_position, int32_t y_position);
HAL_StatusTypeDef Motor_Move_Relative_XY(int32_t x_delta, int32_t y_delta);

// 系统信息接口
uint32_t Motor_Get_System_Uptime(void);
uint32_t Motor_Get_Command_Count(void);
bool Motor_Is_System_Ready(void);
```

### 5.2 调试接口设计 (debug_interface.h)

```c
// 调试系统初始化
HAL_StatusTypeDef Debug_Init(UART_HandleTypeDef* debug_uart);
HAL_StatusTypeDef Debug_DeInit(void);

// 命令处理接口
HAL_StatusTypeDef Debug_Process_Command(const char* command);
HAL_StatusTypeDef Debug_Parse_Command(const char* input, debug_command_t* cmd);
HAL_StatusTypeDef Debug_Execute_Command(const debug_command_t* cmd);

// 状态监控接口
HAL_StatusTypeDef Debug_Enable_Monitor(uint32_t interval_ms);
HAL_StatusTypeDef Debug_Disable_Monitor(void);
HAL_StatusTypeDef Debug_Update_Monitor(void);

// 输出接口
HAL_StatusTypeDef Debug_Printf(const char* format, ...);
HAL_StatusTypeDef Debug_Print_Status(void);
HAL_StatusTypeDef Debug_Print_Help(void);
```

## 6. 关键技术决策

### 6.1 依赖管理策略

#### 6.1.1 最小依赖原则
**移除的依赖**:
- `app_maixcam.h` - 摄像头模块
- `app_pid.h` - PID控制模块  
- `app_trajectory.h` - 复杂轨迹规划
- `app_oled.h` - OLED显示模块
- `app_hmi.h` - 人机交互模块
- `ringbuffer.h` - 环形缓冲区(使用简化版本)
- `MultiTimer.h` - 多定时器(使用简化版本)

**保留的依赖**:
- `stm32f4xx_hal.h` - STM32 HAL库
- `stm32f4xx_hal_uart.h` - UART HAL
- `stm32f4xx_hal_gpio.h` - GPIO HAL
- `stm32f4xx_hal_tim.h` - Timer HAL
- 标准C库 (`stdio.h`, `stdlib.h`, `string.h`, `stdint.h`)

#### 6.1.2 EmmV5驱动适配策略
**原始驱动问题**:
- 依赖过多外部模块
- 包含不必要的功能
- 代码耦合度高

**适配方案**:
1. **接口封装**: 创建`emm_v5_driver.h`封装原始接口
2. **依赖隔离**: 移除对`app_uasrt.h`等模块的依赖
3. **功能精简**: 仅保留核心控制功能
4. **错误处理**: 增强错误检测和处理机制

### 6.2 内存管理策略

#### 6.2.1 静态内存分配
```c
// 全局静态分配，避免动态内存分配
static motor_system_t g_motor_system;
static debug_status_t g_debug_status;
static char g_debug_buffer[256];
static char g_command_buffer[128];
```

#### 6.2.2 内存使用预算
| 模块 | RAM占用 | Flash占用 |
|------|---------|-----------|
| 核心控制 | 2KB | 8KB |
| 调试接口 | 1KB | 4KB |
| 测试功能 | 1KB | 3KB |
| 工具模块 | 0.5KB | 2KB |
| 驱动层 | 0.5KB | 3KB |
| **总计** | **5KB** | **20KB** |

### 6.3 定时器服务设计

#### 6.3.1 简化定时器实现
```c
// 简化的定时器结构体
typedef struct {
    uint32_t interval;          // 定时间隔(ms)
    uint32_t last_tick;         // 上次触发时间
    void (*callback)(void);     // 回调函数
    bool enabled;               // 使能标志
} simple_timer_t;

// 定时器服务接口
HAL_StatusTypeDef Timer_Service_Init(void);
HAL_StatusTypeDef Timer_Start(simple_timer_t* timer, uint32_t interval, void (*callback)(void));
HAL_StatusTypeDef Timer_Stop(simple_timer_t* timer);
void Timer_Service_Update(void);  // 在主循环中调用
```

#### 6.3.2 系统时基
- 使用STM32的SysTick作为系统时基
- 1ms时基精度，满足电机控制需求
- 避免使用复杂的多定时器管理

---

**文档状态**: 🔄 进行中 (第1部分完成)  
**下一步**: 继续完善编译配置和部署策略  
**当前进度**: 架构设计 60% 完成
