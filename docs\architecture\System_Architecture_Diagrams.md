# 独立电机控制模块系统架构图

## 1. 整体系统架构图

```mermaid
graph TB
    subgraph "独立电机控制模块"
        subgraph "调试接口层"
            DI[UART Debug Interface]
            CP[Command Parser]
            SM[Status Monitor]
        end
        
        subgraph "应用层"
            TP[Test Patterns]
            TG[Trajectory Generator]
            FM[File Manager]
        end
        
        subgraph "核心控制层"
            MC[Motor Control]
            CM[Config Manager]
            EH[Error Handler]
        end
        
        subgraph "硬件抽象层"
            ED[EmmV5 Driver]
            UI[UART Interface]
            TS[Timer Service]
        end
        
        subgraph "STM32 HAL层"
            UH[UART HAL]
            GH[GPIO HAL]
            TH[Timer HAL]
        end
    end
    
    %% 连接关系
    DI --> CP
    CP --> SM
    TP --> TG
    TG --> FM
    DI --> MC
    CP --> MC
    MC --> CM
    MC --> EH
    MC --> ED
    ED --> UI
    UI --> TS
    UI --> UH
    TS --> TH
    ED --> GH
    
    %% 外部接口
    USER[用户调试终端] --> DI
    MOTOR_X[X轴电机] --> ED
    MOTOR_Y[Y轴电机] --> ED
    
    classDef debugLayer fill:#e1f5fe
    classDef appLayer fill:#f3e5f5
    classDef coreLayer fill:#e8f5e8
    classDef halLayer fill:#fff3e0
    classDef stmLayer fill:#fce4ec
    
    class DI,CP,SM debugLayer
    class TP,TG,FM appLayer
    class MC,CM,EH coreLayer
    class ED,UI,TS halLayer
    class UH,GH,TH stmLayer
```

## 2. 模块依赖关系图

```mermaid
graph TD
    subgraph "应用层模块"
        A1[debug_interface.h]
        A2[test_trajectory.h]
        A3[file_manager.h]
    end
    
    subgraph "核心层模块"
        C1[motor_control.h]
        C2[motor_config.h]
        C3[motor_types.h]
        C4[error_handler.h]
    end
    
    subgraph "驱动层模块"
        D1[emm_v5_driver.h]
        D2[uart_interface.h]
        D3[timer_service.h]
    end
    
    subgraph "STM32 HAL"
        H1[stm32f4xx_hal_uart.h]
        H2[stm32f4xx_hal_gpio.h]
        H3[stm32f4xx_hal_tim.h]
    end
    
    %% 依赖关系
    A1 --> C1
    A2 --> C1
    A3 --> C4
    
    C1 --> C2
    C1 --> C3
    C1 --> C4
    C2 --> C3
    
    C1 --> D1
    C4 --> D2
    A1 --> D2
    
    D1 --> D2
    D2 --> D3
    
    D1 --> H1
    D1 --> H2
    D2 --> H1
    D3 --> H3
    
    classDef appMod fill:#e1f5fe
    classDef coreMod fill:#e8f5e8
    classDef drvMod fill:#fff3e0
    classDef halMod fill:#fce4ec
    
    class A1,A2,A3 appMod
    class C1,C2,C3,C4 coreMod
    class D1,D2,D3 drvMod
    class H1,H2,H3 halMod
```

## 3. 数据流图

```mermaid
flowchart LR
    subgraph "用户输入"
        CMD[串口命令]
    end
    
    subgraph "命令处理"
        PARSE[命令解析]
        VALID[参数验证]
        EXEC[命令执行]
    end
    
    subgraph "电机控制"
        CTRL[控制逻辑]
        EMMV5[EmmV5协议]
        UART[UART发送]
    end
    
    subgraph "状态反馈"
        RECV[UART接收]
        STATUS[状态解析]
        MONITOR[状态监控]
        RESP[响应输出]
    end
    
    CMD --> PARSE
    PARSE --> VALID
    VALID --> EXEC
    EXEC --> CTRL
    CTRL --> EMMV5
    EMMV5 --> UART
    
    UART --> RECV
    RECV --> STATUS
    STATUS --> MONITOR
    MONITOR --> RESP
    
    %% 错误处理流
    VALID -.->|错误| ERROR[错误处理]
    CTRL -.->|错误| ERROR
    STATUS -.->|错误| ERROR
    ERROR --> RESP
    
    classDef input fill:#e1f5fe
    classDef process fill:#e8f5e8
    classDef control fill:#fff3e0
    classDef feedback fill:#f3e5f5
    classDef error fill:#ffebee
    
    class CMD input
    class PARSE,VALID,EXEC process
    class CTRL,EMMV5,UART control
    class RECV,STATUS,MONITOR,RESP feedback
    class ERROR error
```

## 4. 内存布局图

```mermaid
graph TB
    subgraph "RAM内存布局 (20KB总计)"
        subgraph "系统数据区 (2KB)"
            SYS[motor_system_t<br/>电机系统状态]
            CFG[motor_config_t<br/>电机配置]
        end
        
        subgraph "调试数据区 (1KB)"
            DBG[debug_status_t<br/>调试状态]
            CMD[command_buffer<br/>命令缓冲区]
        end
        
        subgraph "通信缓冲区 (1KB)"
            RX1[UART RX Buffer<br/>接收缓冲区]
            TX1[UART TX Buffer<br/>发送缓冲区]
        end
        
        subgraph "工作缓冲区 (1KB)"
            WORK[临时工作区]
            LOG[日志缓冲区]
        end
        
        subgraph "栈空间 (15KB)"
            STACK[系统栈空间]
        end
    end
    
    subgraph "Flash存储布局 (20KB总计)"
        subgraph "代码区 (16KB)"
            CORE[核心控制代码]
            DEBUG[调试功能代码]
            DRIVER[驱动代码]
        end
        
        subgraph "常量区 (2KB)"
            CONST[常量数据]
            STRINGS[字符串常量]
        end
        
        subgraph "配置区 (2KB)"
            DEFAULT[默认配置]
            PARAMS[参数表]
        end
    end
    
    classDef ramArea fill:#e3f2fd
    classDef flashArea fill:#f1f8e9
    
    class SYS,CFG,DBG,CMD,RX1,TX1,WORK,LOG,STACK ramArea
    class CORE,DEBUG,DRIVER,CONST,STRINGS,DEFAULT,PARAMS flashArea
```

## 5. 时序图 - 电机控制命令执行

```mermaid
sequenceDiagram
    participant User as 用户终端
    participant Debug as 调试接口
    participant Parser as 命令解析器
    participant Motor as 电机控制
    participant Driver as EmmV5驱动
    participant Hardware as 电机硬件
    
    User->>Debug: 发送命令 "MOTOR_SPEED 50 30"
    Debug->>Parser: 解析命令字符串
    Parser->>Parser: 验证参数有效性
    Parser->>Motor: 调用Motor_Set_Speed_XY(50, 30)
    
    Motor->>Motor: 检查电机状态
    Motor->>Driver: 调用Emm_V5_En_Control(X轴, 50%)
    Driver->>Hardware: 发送UART命令到X轴电机
    Hardware-->>Driver: 返回执行状态
    Driver-->>Motor: 返回执行结果
    
    Motor->>Driver: 调用Emm_V5_En_Control(Y轴, 30%)
    Driver->>Hardware: 发送UART命令到Y轴电机
    Hardware-->>Driver: 返回执行状态
    Driver-->>Motor: 返回执行结果
    
    Motor->>Motor: 更新电机状态
    Motor-->>Parser: 返回执行结果
    Parser-->>Debug: 返回命令执行状态
    Debug->>User: 输出执行结果 "OK: X=50% Y=30%"
    
    Note over Debug,Motor: 整个过程耗时 < 10ms
```

## 6. 状态机图 - 电机控制状态

```mermaid
stateDiagram-v2
    [*] --> UNINITIALIZED
    
    UNINITIALIZED --> INITIALIZING : Motor_System_Init()
    INITIALIZING --> IDLE : 初始化成功
    INITIALIZING --> ERROR : 初始化失败
    
    IDLE --> SPEED_CONTROL : Motor_Set_Speed()
    IDLE --> POSITION_CONTROL : Motor_Set_Position()
    IDLE --> TEST_MODE : 测试命令
    
    SPEED_CONTROL --> IDLE : Motor_Stop()
    SPEED_CONTROL --> ERROR : 通信错误
    SPEED_CONTROL --> SPEED_CONTROL : 速度更新
    
    POSITION_CONTROL --> IDLE : 到达目标位置
    POSITION_CONTROL --> IDLE : Motor_Stop()
    POSITION_CONTROL --> ERROR : 通信错误
    
    TEST_MODE --> IDLE : 测试完成
    TEST_MODE --> ERROR : 测试失败
    
    ERROR --> IDLE : 错误恢复
    ERROR --> UNINITIALIZED : 系统重置
    
    note right of SPEED_CONTROL
        速度控制模式
        - 实时速度调节
        - 连续运行
    end note
    
    note right of POSITION_CONTROL
        位置控制模式
        - 目标位置运动
        - 自动停止
    end note
```

## 7. 部署架构图

```mermaid
graph TB
    subgraph "开发环境"
        KEIL[Keil MDK-ARM]
        GIT[Git版本控制]
        DEBUG_TOOL[串口调试工具]
    end
    
    subgraph "编译产物"
        HEX[HEX固件文件]
        MAP[MAP映射文件]
        LST[LST列表文件]
    end
    
    subgraph "目标硬件"
        STM32[STM32F407]
        MOTOR_X[X轴电机]
        MOTOR_Y[Y轴电机]
        UART_DEBUG[调试串口]
    end
    
    subgraph "测试环境"
        TEST_SCRIPT[测试脚本]
        PERF_MONITOR[性能监控]
        LOG_ANALYZER[日志分析]
    end
    
    KEIL --> HEX
    KEIL --> MAP
    KEIL --> LST
    
    HEX --> STM32
    STM32 --> MOTOR_X
    STM32 --> MOTOR_Y
    STM32 --> UART_DEBUG
    
    UART_DEBUG --> DEBUG_TOOL
    DEBUG_TOOL --> TEST_SCRIPT
    TEST_SCRIPT --> PERF_MONITOR
    PERF_MONITOR --> LOG_ANALYZER
    
    GIT --> KEIL
    
    classDef devEnv fill:#e1f5fe
    classDef buildOutput fill:#e8f5e8
    classDef hardware fill:#fff3e0
    classDef testEnv fill:#f3e5f5
    
    class KEIL,GIT,DEBUG_TOOL devEnv
    class HEX,MAP,LST buildOutput
    class STM32,MOTOR_X,MOTOR_Y,UART_DEBUG hardware
    class TEST_SCRIPT,PERF_MONITOR,LOG_ANALYZER testEnv
```

---

**架构图文档状态**: ✅ 已完成  
**包含图表**: 7个核心架构图  
**下一步**: 开始详细代码实现
