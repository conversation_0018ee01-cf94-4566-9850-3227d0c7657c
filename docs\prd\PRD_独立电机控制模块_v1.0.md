# 独立电机控制模块产品需求文档 (PRD)

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-29 |
| 负责人 | Emma (产品经理) |
| 项目名称 | 独立电机控制模块 |
| 版权归属 | 米醋电子工作室 |

### 版本历史
- v1.0 (2025-01-29): 初始版本，定义独立电机控制模块的完整需求

## 2. 背景与问题陈述

### 2.1 当前问题
当前项目中的电机控制模块与多个其他模块存在紧密耦合：
- 依赖陀螺仪模块 (app_maixcam.h)
- 依赖摄像头模块 (app_maixcam.h) 
- 依赖PID控制模块 (app_pid.h)
- 依赖轨迹规划模块 (app_trajectory.h)
- 依赖OLED显示模块 (app_oled.h)
- 依赖HMI人机交互模块 (app_hmi.h)

### 2.2 核心痛点
1. **调试困难**: 无法单独测试电机控制功能，必须启动整个系统
2. **开发效率低**: 电机参数调试需要加载所有模块，编译时间长
3. **资源浪费**: 简单的电机测试需要占用大量系统资源
4. **维护复杂**: 电机控制逻辑与业务逻辑混合，难以维护
5. **调试文件管理**: 缺乏便于删除的调试文件管理机制

### 2.3 解决目标
创建一个完全独立的电机控制模块，可以：
- 不依赖陀螺仪和摄像头即可运行
- 提供完整的电机调试功能
- 便于快速测试和参数调优
- 支持调试文件的便捷管理和清理

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **模块独立性**: 创建完全独立的电机控制模块，零外部依赖
2. **调试便利性**: 提供丰富的调试接口和工具
3. **开发效率**: 显著提升电机调试和开发效率
4. **文件管理**: 实现调试文件的智能管理和清理

### 3.2 关键结果 (Key Results)
1. **编译时间**: 独立模块编译时间 < 30秒 (相比完整项目 > 2分钟)
2. **内存占用**: 独立模块RAM占用 < 20KB (相比完整项目 > 100KB)
3. **启动时间**: 模块启动时间 < 3秒 (相比完整项目 > 10秒)
4. **调试效率**: 电机参数调试时间减少 70%
5. **文件清理**: 调试文件可一键清理，清理率 100%

### 3.3 反向指标 (Counter Metrics)
1. **功能完整性**: 不能影响原有电机控制功能的完整性
2. **接口兼容性**: 必须保持与原系统的接口兼容性
3. **稳定性**: 不能降低电机控制的稳定性和精度

## 4. 用户画像与用户故事

### 4.1 目标用户
1. **嵌入式开发工程师**: 需要快速调试电机参数
2. **测试工程师**: 需要独立测试电机控制功能
3. **产品工程师**: 需要验证电机控制性能
4. **维护工程师**: 需要快速定位电机相关问题

### 4.2 用户故事

#### 故事1: 快速电机调试
**作为** 嵌入式开发工程师  
**我希望** 能够快速启动电机控制模块进行参数调试  
**以便** 在不启动整个系统的情况下优化电机性能  

**验收标准**:
- 模块启动时间 < 3秒
- 支持串口命令实时调整参数
- 提供实时状态反馈

#### 故事2: 独立功能测试
**作为** 测试工程师  
**我希望** 能够独立测试电机控制功能  
**以便** 验证电机控制的准确性和稳定性  

**验收标准**:
- 可独立运行，无需其他模块
- 提供完整的测试接口
- 支持自动化测试脚本

#### 故事3: 调试文件管理
**作为** 开发工程师  
**我希望** 调试过程中产生的临时文件能够便于管理和清理  
**以便** 保持项目目录的整洁，避免调试文件污染正式代码  

**验收标准**:
- 调试文件统一存放在指定目录
- 支持一键清理所有调试文件
- 清理后不影响正常功能

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 独立电机控制核心
**功能描述**: 提供基础的电机控制功能，完全独立于其他模块

**主要特性**:
- 支持双轴电机控制 (X轴、Y轴)
- 速度控制模式 (百分比控制)
- 位置控制模式 (脉冲控制)
- 电机启停控制
- 参数实时调整

**技术规格**:
- 通信协议: EmmV5串口协议
- 控制精度: ±1脉冲
- 响应时间: < 10ms
- 支持速度范围: 0-100% (对应0-20RPM)

#### 5.1.2 串口调试接口
**功能描述**: 提供丰富的串口命令接口，支持实时调试

**命令集合**:
```
MOTOR_INIT          - 初始化电机
MOTOR_SPEED x y     - 设置XY轴速度 (x,y: -100~100)
MOTOR_POS x y       - 设置XY轴位置 (x,y: 脉冲数)
MOTOR_STOP          - 停止所有电机
MOTOR_STATUS        - 查询电机状态
MOTOR_SET_PARAM     - 设置电机参数
MOTOR_GET_PARAM     - 获取电机参数
MOTOR_TEST_CIRCLE   - 执行圆形测试轨迹
MOTOR_TEST_SQUARE   - 执行方形测试轨迹
DEBUG_ON            - 开启调试模式
DEBUG_OFF           - 关闭调试模式
```

#### 5.1.3 状态监控系统
**功能描述**: 实时监控电机状态，提供详细的运行信息

**监控内容**:
- 电机位置 (实时脉冲数)
- 电机速度 (实时RPM)
- 电机状态 (运行/停止/错误)
- 通信状态 (正常/异常)
- 系统资源使用情况

**输出格式**:
```
[MOTOR_STATUS] X_POS:1234 Y_POS:5678 X_SPEED:15 Y_SPEED:20 STATUS:RUNNING
[MOTOR_ERROR] X_COMM_FAIL Y_COMM_OK ERROR_CODE:0x01
[SYSTEM_INFO] RAM:15KB UPTIME:00:05:23 CMD_COUNT:156
```

#### 5.1.4 测试轨迹生成器
**功能描述**: 内置常用测试轨迹，便于快速验证电机性能

**内置轨迹**:
1. **圆形轨迹**: 可调半径和速度的圆形运动
2. **方形轨迹**: 可调边长的方形运动  
3. **直线轨迹**: 指定起点终点的直线运动
4. **正弦轨迹**: 正弦波形轨迹运动
5. **自定义轨迹**: 支持用户定义轨迹点

**参数配置**:
- 轨迹大小 (半径、边长等)
- 运动速度 (1-100%)
- 循环次数 (1-无限)
- 暂停时间 (轨迹间隔)

#### 5.1.5 调试文件管理系统
**功能描述**: 智能管理调试过程中产生的文件，便于清理

**管理功能**:
- 调试日志自动分类存储
- 测试数据文件统一管理
- 临时配置文件跟踪
- 一键清理调试文件
- 清理前安全检查

**文件分类**:
```
debug/
├── logs/           # 调试日志文件
├── test_data/      # 测试数据文件  
├── temp_config/    # 临时配置文件
├── screenshots/    # 调试截图 (如果有)
└── reports/        # 测试报告
```

### 5.2 系统架构设计

#### 5.2.1 模块结构
```
MotorDebugModule/
├── Core/                   # 核心电机控制
│   ├── motor_control.c/h   # 电机控制核心
│   ├── motor_config.c/h    # 电机配置管理
│   └── motor_types.h       # 数据类型定义
├── Debug/                  # 调试功能
│   ├── debug_interface.c/h # 调试接口
│   ├── debug_commands.c/h  # 命令解析
│   ├── debug_monitor.c/h   # 状态监控
│   └── debug_logger.c/h    # 日志记录
├── Test/                   # 测试功能
│   ├── test_trajectory.c/h # 测试轨迹
│   ├── test_patterns.c/h   # 测试模式
│   └── test_utils.c/h      # 测试工具
├── Utils/                  # 工具模块
│   ├── file_manager.c/h    # 文件管理
│   ├── string_utils.c/h    # 字符串工具
│   └── math_utils.c/h      # 数学工具
└── Config/                 # 配置文件
    ├── motor_config.h      # 电机配置
    ├── debug_config.h      # 调试配置
    └── system_config.h     # 系统配置
```

#### 5.2.2 依赖关系
**最小依赖集合**:
- STM32 HAL库 (UART, GPIO, Timer)
- EmmV5电机驱动库
- 基础C标准库 (string.h, stdio.h, stdlib.h)

**移除的依赖**:
- app_maixcam.h (摄像头模块)
- app_pid.h (PID控制模块)  
- app_trajectory.h (轨迹规划模块)
- app_oled.h (OLED显示模块)
- app_hmi.h (人机交互模块)
- ringbuffer.h (环形缓冲区，使用简化版本)

## 6. 范围定义

### 6.1 包含功能 (In Scope)
1. **基础电机控制**: 速度控制、位置控制、启停控制
2. **串口调试接口**: 完整的命令集和状态反馈
3. **状态监控**: 实时电机状态和系统信息
4. **测试轨迹**: 内置常用测试模式
5. **文件管理**: 调试文件的管理和清理
6. **参数配置**: 电机参数的实时调整和保存
7. **错误处理**: 完善的错误检测和恢复机制
8. **性能监控**: 系统资源使用情况监控

### 6.2 排除功能 (Out of Scope)
1. **复杂轨迹规划**: 不包含高级轨迹算法
2. **PID控制**: 不包含闭环PID控制功能
3. **图形界面**: 不包含GUI界面，仅支持串口
4. **网络通信**: 不包含WiFi、蓝牙等网络功能
5. **传感器集成**: 不包含陀螺仪、摄像头等传感器
6. **数据存储**: 不包含复杂的数据存储功能
7. **多任务调度**: 使用简化的任务调度机制

## 7. 依赖与风险

### 7.1 内部依赖项
1. **EmmV5驱动库**: 必须保持与现有驱动的兼容性
2. **串口资源**: 需要独占一个串口用于调试
3. **定时器资源**: 需要一个定时器用于状态监控
4. **内存资源**: 需要预留足够的RAM和Flash空间

### 7.2 外部依赖项
1. **开发工具**: Keil MDK-ARM开发环境
2. **硬件平台**: STM32F407系列微控制器
3. **调试工具**: 串口调试助手或自定义上位机
4. **测试设备**: 电机硬件和测试平台

### 7.3 潜在风险
1. **兼容性风险**: 独立模块可能与原系统存在接口不兼容
2. **性能风险**: 简化后的模块可能影响电机控制精度
3. **资源风险**: 独立模块可能占用过多系统资源
4. **维护风险**: 两套代码可能导致维护复杂度增加

### 7.4 风险缓解策略
1. **接口标准化**: 定义标准的电机控制接口，确保兼容性
2. **性能测试**: 严格的性能测试，确保控制精度不降低
3. **资源优化**: 精心设计，最小化资源占用
4. **代码复用**: 最大化代码复用，减少维护负担

## 8. 发布初步计划

### 8.1 开发阶段
**阶段1: 核心模块开发** (预计3天)
- 创建独立的电机控制核心
- 实现基础的速度和位置控制
- 完成串口通信接口

**阶段2: 调试功能开发** (预计2天)  
- 实现串口命令解析
- 添加状态监控功能
- 完成调试日志系统

**阶段3: 测试功能开发** (预计2天)
- 实现测试轨迹生成器
- 添加自动化测试功能
- 完成文件管理系统

**阶段4: 集成测试** (预计1天)
- 完整功能测试
- 性能基准测试
- 兼容性验证

### 8.2 测试计划
1. **单元测试**: 每个模块的独立功能测试
2. **集成测试**: 模块间接口和协作测试
3. **性能测试**: 响应时间、精度、资源占用测试
4. **兼容性测试**: 与原系统的接口兼容性测试
5. **压力测试**: 长时间运行和极限参数测试

### 8.3 部署策略
1. **并行开发**: 与原系统并行开发，不影响主线
2. **渐进集成**: 逐步替换原系统中的电机控制部分
3. **回滚机制**: 保留原系统，确保可以快速回滚
4. **文档同步**: 同步更新技术文档和用户手册

---

**文档状态**: ✅ 已完成  
**下一步**: 进入架构设计阶段  
**负责人**: Emma -> Bob (架构师)
